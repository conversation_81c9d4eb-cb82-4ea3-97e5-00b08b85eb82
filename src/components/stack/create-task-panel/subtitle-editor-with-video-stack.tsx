"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Plus, Trash2, Wand2, Loader2, Play, Pause, Languages, AudioLines } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState, useEffect, useRef, useCallback, useMemo } from "react";
import Image from "next/image";

import { useAtomValue, useSetAtom, atom } from "jotai";
import {
  subtitleDataAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  SubtitleItem,
  SubtitleData
} from "@/stores/slices/subtitle_store";
import { currentTaskAtom } from "@/stores/slices/current_task";
import { ArtPlayer } from "@/components/common/art-player";

import { isYoutubeUrl, getYoutubeVideoId } from "@/utils/video-format";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { env } from "@/env";

// Subtitle overlay component for video player
interface SubtitleOverlayProps {
  subtitle: SubtitleItem | null;
  className?: string;
}

const SubtitleOverlay: React.FC<SubtitleOverlayProps> = ({ subtitle, className }) => {
  if (!subtitle || (!subtitle.text && !subtitle.translatedText)) {
    return null;
  }

  return (
    <div className={cn("absolute inset-0 pointer-events-none flex flex-col justify-end p-4", className)}>
      {subtitle.translatedText && (
        <div className="text-center mb-2">
          <div className="inline-block bg-black/70 text-white px-3 py-1 rounded text-sm font-medium">
            {subtitle.translatedText}
          </div>
        </div>
      )}
      {subtitle.text && (
        <div className="text-center">
          <div className="inline-block bg-black/70 text-white px-3 py-1 rounded text-sm">
            {subtitle.text}
          </div>
        </div>
      )}
    </div>
  );
};

interface SubtitleEditorWithVideoStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  editingField: string | null;
  onFieldEdit: (field: string | null) => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
  isActive: boolean;
  onSeekTo: (time: number) => void;
}

// Convert time string to seconds
const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds || '0') / 1000);
};

// Convert seconds to time string
const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

// SRT Parser
const parseSrtToSubtitleItems = (srtContent: string): SubtitleItem[] => {
  const items: SubtitleItem[] = [];
  const lines = srtContent.split(/\r?\n/);
  let i = 0;
  while (i < lines.length) {
    const idLine = lines[i];
    if (!idLine || /^\s*$/.test(idLine)) { // Skip empty lines between entries
      i++;
      continue;
    }

    // Skip index number line (optional, we generate our own IDs)
    // const index = parseInt(idLine, 10);
    i++;
    if (i >= lines.length) break;

    const timeLine = lines[i];
    const timeMatch = timeLine.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
    if (!timeMatch) {
      // Invalid time format, skip this entry or handle error
      // For now, try to find next valid entry
      while(i < lines.length && !lines[i].match(/^\d+$/) && !lines[i].match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/)) {
        i++;
      }
      if (i < lines.length && lines[i].match(/^\d+$/)) { // Found next index
        // continue to next iteration
      } else {
        i++; // skip current line and continue
      }
      continue;
    }
    const startTime = timeMatch[1];
    const endTime = timeMatch[2];
    i++;

    const textLines: string[] = [];
    while (i < lines.length && lines[i]) {
      textLines.push(lines[i]);
      i++;
    }
    
    let text = "";
    let translatedText = "";

    if (textLines.length === 1) {
      text = textLines[0];
    } else if (textLines.length >= 2) {
      text = textLines[0]; // First line as original
      translatedText = textLines.slice(1).join('\n'); // Rest as translation
    }
    
    if (text || translatedText) { // Only add if there's some text
        items.push({
            id: crypto.randomUUID(),
            startTime,
            endTime,
            text,
            translatedText: translatedText || undefined,
        });
    }

    // Skip empty line after text block if present
    if (i < lines.length && !lines[i]) {
      i++;
    }
  }
  return items;
};


const SubtitleRow = ({
  subtitle,
  editingField, 
  onFieldEdit, 
  onSave, 
  onDelete,
  isActive,
  onSeekTo
}: SubtitleRowProps) => {
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  // Update edit data when subtitle changes
  useEffect(() => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
  }, [subtitle]);

  const handleSave = (field: string) => {
    const updates: Partial<SubtitleItem> = {};
    
    switch (field) {
      case 'startTime':
        updates.startTime = editData.startTime;
        break;
      case 'endTime':
        updates.endTime = editData.endTime;
        break;
      case 'text':
        updates.text = editData.text;
        break;
      case 'translatedText':
        updates.translatedText = editData.translatedText;
        break;
    }
    
    onSave(updates);
    onFieldEdit(null);
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onFieldEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent, field: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave(field);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSeekToStart = () => {
    const startSeconds = timeStringToSeconds(subtitle.startTime);
    onSeekTo(startSeconds);
  };

  return (
    <div
      className={cn(
        "grid grid-cols-6 gap-2 p-3 border-b hover:bg-muted/30 transition-colors cursor-pointer",
        editingField && "bg-primary/5 border-primary/20",
        isActive && "bg-primary/10 border-primary/30 shadow-sm"
      )}
      onClick={handleSeekToStart}
      data-subtitle-id={subtitle.id}
    >
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'startTime' ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'startTime')}
            onBlur={() => handleSave('startTime')}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('startTime');
            }}
          >
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'endTime' ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'endTime')}
            onBlur={() => handleSave('endTime')}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('endTime');
            }}
          >
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'text' ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'text')}
            onBlur={() => handleSave('text')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('text');
            }}
          >
            {subtitle.text || (
              <span className="text-muted-foreground italic">Click to add text...</span>
            )}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'translatedText' ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'translatedText')}
            onBlur={() => handleSave('translatedText')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('translatedText');
            }}
          >
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">Click to add translation...</span>
            )}
          </div>
        )}
      </div>

      {/* Duration */}
      <div className="flex flex-col gap-1 justify-center">
        <div className="text-xs text-muted-foreground text-center">
          {((timeStringToSeconds(subtitle.endTime) - timeStringToSeconds(subtitle.startTime))).toFixed(1)}s
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        <div className="flex flex-col gap-1">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }} 
            className="h-7"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {editingField && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={(e) => {
                e.stopPropagation();
                handleCancel();
              }} 
              className="h-7 text-xs"
            >
              ESC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const SubtitleEditorWithVideoStack = ({ stackRef }: SubtitleEditorWithVideoStackProps) => {
  console.log('[COMPONENT] SubtitleEditorWithVideoStack render');

  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const setSubtitleData = useSetAtom(subtitleDataAtom);
  const currentTask = useAtomValue(currentTaskAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);

  // State for tracking which field is being edited
  const [editingField, setEditingField] = useState<{id: string, field: string} | null>(null);
  const [currentTime, setCurrentTime] = useState(0);

  // AI prompt state
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [promptText, setPromptText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);

  // Subtitle overlay state
  const [currentSubtitle, setCurrentSubtitle] = useState<SubtitleItem | null>(null);

  // Refs for video player and subtitle list
  const artPlayerRef = useRef<any>(null);
  const subtitleListRef = useRef<HTMLDivElement>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);

  // Simple timer state for subtitle preview (independent of video)
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoReady, setVideoReady] = useState(false);
  const [isVideoFollowing, setIsVideoFollowing] = useState(false);

  // Manual timer fallback (only when video is not available)
  useEffect(() => {
    // Only use manual timer if no video player exists or video is not ready
    if (isTimerRunning && (!artPlayerRef.current || !videoReady)) {
      console.log('[TIMER] Starting manual timer - isTimerRunning:', isTimerRunning, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
      const interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 0.1;
          console.log('[TIMER] Manual timer update:', newTime.toFixed(1));
          return newTime;
        });
      }, 100);
      return () => {
        console.log('[TIMER] Stopping manual timer');
        clearInterval(interval);
      };
    } else {
      console.log('[TIMER] Manual timer not started - isTimerRunning:', isTimerRunning, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
    }
  }, [isTimerRunning, videoReady]);

  // Find current subtitle based on video time
  const findCurrentSubtitle = (time: number): SubtitleItem | null => {
    return subtitleData.originalSubtitles.find(subtitle => {
      const startTime = timeStringToSeconds(subtitle.startTime);
      const endTime = timeStringToSeconds(subtitle.endTime);
      return time >= startTime && time <= endTime;
    }) || null;
  };

  // Update current subtitle when time changes
  useEffect(() => {
    const newCurrentSubtitle = findCurrentSubtitle(currentTime);
    setCurrentSubtitle(newCurrentSubtitle);
  }, [currentTime, subtitleData.originalSubtitles]);

  // Reset video state when video URL changes
  useEffect(() => {
    console.log('[VIDEO] Video URL changed, resetting states. New URL:', currentTask.videoUrl);
    setVideoReady(false);
    setIsVideoPlaying(false);
    setIsTimerRunning(false);
  }, [currentTask.videoUrl]);

  // Auto-scroll subtitle list to current subtitle (with debouncing)
  useEffect(() => {
    if (currentSubtitle && subtitleListRef.current) {
      // Debounce the scroll to prevent too frequent updates
      const timeoutId = setTimeout(() => {
        try {
          const subtitleElement = subtitleListRef.current?.querySelector(`[data-subtitle-id="${currentSubtitle.id}"]`);
          if (subtitleElement) {
            subtitleElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        } catch (error) {
          console.warn('Failed to scroll to subtitle:', error);
        }
      }, 200);

      return () => clearTimeout(timeoutId);
    }
  }, [currentSubtitle]);

  // Video scroll following effect
  useEffect(() => {
    const subtitleList = subtitleListRef.current;
    if (!subtitleList) return;

    let scrollTimeout: NodeJS.Timeout;

    const handleScroll = () => {
      // Set isVideoFollowing to true immediately on scroll
      setIsVideoFollowing(true);
      // console.log('[SCROLL] Subtitle scroll detected, video following styles applied');

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        setIsVideoFollowing(false);
        // console.log('[SCROLL] Scroll stopped, video following styles removed');
      }, 1000); // Reset after 1 second of no scrolling
    };

    subtitleList.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      subtitleList.removeEventListener('scroll', handleScroll);
      clearTimeout(scrollTimeout);
    };
  }, []);

  // Handle adding subtitle at current time
  const handleAddSubtitle = () => {
    const startTime = secondsToTimeString(currentTime);
    const endTime = secondsToTimeString(currentTime + 3); // Default 3 second duration

    addSubtitle({
      startTime,
      endTime,
      text: "",
      translatedText: ""
    });
  };

  // Handle seeking to specific time - update timer and optionally seek video
  const handleSeekTo = (time: number) => {
    console.log('[SEEK] Seeking to time:', time, 'videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);
    setCurrentTime(time);
    // Try to seek video player if available (but don't break if it fails)
    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[SEEK] Attempting video seek to:', time);
        artPlayerRef.current.seek = time;
        console.log('[SEEK] Video seek successful');
      } catch (error) {
        // Silently ignore seek errors to prevent breaking video
        console.warn('[SEEK] Video seek failed, continuing with timer only:', error);
      }
    } else {
      console.log('[SEEK] Video seek skipped - not ready or no player');
    }
  };

  // Simple video controls - completely rewritten to avoid conflicts
  const handleVideoPlay = () => {
    console.log('[PLAY] Play button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current, 'isVideoPlaying:', isVideoPlaying, 'isTimerRunning:', isTimerRunning);

    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[PLAY] Attempting to play video');
        // Don't set state here - let the video events handle it
        artPlayerRef.current.play();
        console.log('[PLAY] Video play() called successfully');
      } catch (error) {
        console.warn('[PLAY] Video play failed, using manual timer:', error);
        setIsTimerRunning(true);
        setIsVideoPlaying(false);
      }
    } else {
      // No video, just start manual timer
      console.log('[PLAY] No video available, starting manual timer');
      setIsTimerRunning(true);
      setIsVideoPlaying(false);
    }
  };

  const handleVideoPause = () => {
    console.log('[PAUSE] Pause button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current, 'isVideoPlaying:', isVideoPlaying, 'isTimerRunning:', isTimerRunning);

    if (artPlayerRef.current && videoReady) {
      try {
        console.log('[PAUSE] Attempting to pause video');
        // Don't set state here - let the video events handle it
        artPlayerRef.current.pause();
        console.log('[PAUSE] Video pause() called successfully');
      } catch (error) {
        console.warn('[PAUSE] Video pause failed:', error);
        setIsTimerRunning(false);
        setIsVideoPlaying(false);
      }
    } else {
      // No video, just stop manual timer
      console.log('[PAUSE] No video available, stopping manual timer');
      setIsTimerRunning(false);
      setIsVideoPlaying(false);
    }
  };

  const handleSyncWithVideo = () => {
    console.log('[SYNC] Sync button clicked - videoReady:', videoReady, 'hasArtPlayer:', !!artPlayerRef.current);

    if (artPlayerRef.current && videoReady) {
      try {
        const videoTime = artPlayerRef.current.currentTime || 0;
        console.log('[SYNC] Current video time:', videoTime);
        setCurrentTime(videoTime);
        toast.success(`Synced to video time: ${videoTime.toFixed(1)}s`);
      } catch (error) {
        console.error('[SYNC] Could not sync with video time:', error);
        toast.error('Could not sync with video time');
      }
    } else {
      console.warn('[SYNC] Video not ready for sync');
      toast.error('Video not ready for sync');
    }
  };

  const handleGenerateAISubtitles = async () => {
    if (!promptText.trim()) {
      toast.error("Please enter a description for the AI (e.g., 'this cooking video').");
      return;
    }

    // Get languages from current task settings
    const sourceLanguage = currentTask.settings?.sourceLanguage || "en"; // Default to 'en' if not set
    const targetLanguage = currentTask.settings?.targetLanguage || "zh"; // Default to 'zh' if not set
    const promptSourceDescription = promptText; // User's input describes the source

    const structuredPrompt = `You are an expert multilingual transcription and translation service.
    Your task is to transcribe audio from ${promptSourceDescription} and then immediately translate each line.
    The original audio is in ${sourceLanguage}.
    Translate each transcribed line into ${targetLanguage}.

    Provide the output as a sequence of subtitle entries.
    For EACH subtitle entry, you MUST follow this exact multi-line structure:
    <index_number>
    <start_timestamp> --> <end_timestamp>  (timestamp format: HH:MM:SS,mmm)
    <text_in_${sourceLanguage}>
    <text_in_${targetLanguage}>

    (Ensure there is ONE blank line separating each complete subtitle entry from the next index_number. Do not add extra blank lines within an entry.)

    Generate approximately 10-15 subtitle entries.
    Ensure timestamps are sequential and represent realistic speech segment durations (e.g., 2-7 seconds per line, with short gaps between them).
    The first subtitle should start after 00:00:00,500.

    Example of a single entry:
    1
    00:00:01,234 --> 00:00:04,567
    This is the original sentence in ${sourceLanguage}.
    This is the translated sentence in ${targetLanguage}.

    Do not include any other explanations, introductory text, or any text outside of this specified SRT-like bilingual format.
    The entire response should be only the structured subtitle entries.`;

    setIsGenerating(true);
    try {
      const response = await fetch('https://api.302.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer sk-gJj2ExFbK8qdFbVgfRQY9FfmpYkte8PGCkI4LCVoBnitvkgx', // IMPORTANT: Use environment variables in production
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: "gemini-1.5-pro",
          messages: [{ role: "user", content: structuredPrompt }],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();
      
      // Assuming the SRT content is in responseData.choices[0].message.content
      const srtContent = responseData?.choices?.[0]?.message?.content;

      if (!srtContent || typeof srtContent !== 'string') {
        console.error("Invalid response structure from AI:", responseData);
        throw new Error("Failed to extract SRT content from AI response.");
      }
      
      const newSubtitles = parseSrtToSubtitleItems(srtContent);

      if (newSubtitles.length === 0 && srtContent.trim() !== "") {
         // This means parsing might have failed or SRT was empty/malformed but not entirely whitespace
         toast.warning("AI generated content, but it could not be parsed into subtitles. Please check the format.");
      } else if (newSubtitles.length === 0) {
        toast.info("AI did not generate any subtitles.");
      } else {
        toast.success(`Successfully generated ${newSubtitles.length} subtitle(s)!`);
      }

      setSubtitleData((currentData: SubtitleData) => ({
        ...currentData,
        originalSubtitles: newSubtitles,
        // Create a deep copy for translatedSubtitles to ensure they are distinct objects
        // if they are meant to be independently editable or represent a different translation state.
        // If translatedText within SubtitleItem is the sole source of translation, this mapping might differ.
        translatedSubtitles: newSubtitles.map(s => ({ ...s })),
      }));
      setShowPromptInput(false); // Optionally hide prompt input after generation
      setPromptText(""); // Optionally clear prompt

    } catch (error) {
      console.error("Error generating subtitles with AI:", error);
      toast.error(`Failed to generate subtitles: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTranslateAllWithAI = async () => {
    if (subtitleData.originalSubtitles.length === 0) {
      toast.info("No original subtitles to translate.");
      return;
    }

    setIsTranslating(true);

    const sourceLanguage = currentTask.settings?.sourceLanguage || "en";
    const targetLanguage = currentTask.settings?.targetLanguage || "zh";
    
    // Prepare inputText from existing original subtitles
    const inputText = subtitleData.originalSubtitles.map((sub, index) => `${index + 1}\n${sub.startTime} --> ${sub.endTime}\n${sub.text}`).join('\n\n');
    // The prompt asks for "Input transcript is", but the example shows full SRT-like entries.
    // For now, let's assume the AI can handle the original text lines directly or we adjust the prompt.
    // Let's try sending just the text lines for now, as the prompt is complex.
    // const inputTextForPrompt = subtitleData.originalSubtitles.map(sub => sub.text).join('\n');
    // For the new prompt, it seems it expects the full SRT-like structure for the input.
    const promptSourceDescription = "the provided transcript";


    const translationPrompt = `You are an expert multilingual transcription and translation service.
    Your task is to transcribe audio from ${promptSourceDescription} and then immediately translate each line.
    The original language is in ${sourceLanguage}.
    Translate each original language line into ${targetLanguage}.
Input transcript is
${inputText}

    Provide the output as a sequence of subtitle entries.
    For EACH subtitle entry, you MUST follow this exact multi-line structure:
    <index_number>
    <start_timestamp> --> <end_timestamp>  (timestamp format: HH:MM:SS,mmm)
    <text_in_${sourceLanguage}>
    <text_in_${targetLanguage}>

    (Ensure there is ONE blank line separating each complete subtitle entry from the next index_number. Do not add extra blank lines within an entry.)

    Generate approximately ${subtitleData.originalSubtitles.length} subtitle entries.
    Ensure timestamps are sequential and represent realistic speech segment durations (e.g., 2-7 seconds per line, with short gaps between them).
    The first subtitle should start after 00:00:00,500.

    Example of a single entry:
    1
    00:00:01,234 --> 00:00:04,567
    This is the original sentence in ${sourceLanguage}.
    This is the translated sentence in ${targetLanguage}.

    Do not include any other explanations, introductory text, or any text outside of this specified SRT-like bilingual format.
    The entire response should be only the structured subtitle entries.`;

    try {
      const response = await fetch('https://api.302.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer sk-gJj2ExFbK8qdFbVgfRQY9FfmpYkte8PGCkI4LCVoBnitvkgx',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: "gemini-1.5-pro",
          messages: [{ role: "user", content: translationPrompt }],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();
      const srtContent = responseData?.choices?.[0]?.message?.content;

      if (!srtContent || typeof srtContent !== 'string') {
        console.error("Invalid response structure from AI translation:", responseData);
        throw new Error("Failed to extract SRT content from AI translation response.");
      }

      const translatedItems = parseSrtToSubtitleItems(srtContent);

      if (translatedItems.length === 0 && srtContent.trim() !== "") {
        toast.warning("AI translated content, but it could not be parsed. Please check format.");
      } else if (translatedItems.length === 0) {
        toast.info("AI did not generate any translations.");
      } else {
        toast.success(`Successfully translated ${translatedItems.length} subtitle(s)!`);
      }
      
      setSubtitleData((currentSubData: SubtitleData) => {
        const updatedOriginals = currentSubData.originalSubtitles.map((originalSub, index) => {
          const translatedMatch = translatedItems.find(tItem => tItem.startTime === originalSub.startTime && tItem.endTime === originalSub.endTime);
          // If AI returns text for original language, update it. Otherwise, keep existing.
          // Always update translatedText if a match is found.
          if (translatedMatch) {
            return {
              ...originalSub,
              text: translatedMatch.text || originalSub.text,
              translatedText: translatedMatch.translatedText || "", // Ensure translatedText is always a string
            };
          }
          return originalSub;
        });
        return {
          ...currentSubData,
          originalSubtitles: updatedOriginals,
          translatedSubtitles: updatedOriginals.map(s => ({...s, translatedText: s.translatedText || ""})),
        };
      });

    } catch (error) {
      console.error("Error translating subtitles with AI:", error);
      toast.error(`Failed to translate subtitles: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsTranslating(false);
    }
  };

  const handleExtractSubtitleFromAudio = async () => {
    // Get the video URL from current task
    const videoUrl = currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success'
      ? currentTask.DownloadTask.videoUrl
      : currentTask.videoUrl;

    if (!videoUrl) {
      toast.error("No video URL available for audio extraction.");
      return;
    }

    // Get source language from current task settings
    const sourceLanguage = currentTask.settings?.sourceLanguage || "zh"; // Default to 'zh' as per the example

    setIsExtracting(true);

    try {
      // Use local API host if available, otherwise fall back to relative path
      const apiHost = env.NEXT_PUBLIC_BASE_API_HOST || '';
      const recognEndpoint = apiHost ? `${apiHost}/recogn` : '/api/recogn';

      const response = await fetch(recognEndpoint, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: videoUrl,
          recogn_type: 0,
          split_type: "overall",
          model_name: "tiny",
          is_cuda: false,
          detect_language: sourceLanguage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(`API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
      }

      const responseData = await response.json();

      // Handle the response - this will depend on what the API returns
      // For now, we'll just show a success message
      console.log('Audio extraction response:', responseData);
      toast.success("Audio extraction request sent successfully!");

      // TODO: Parse the response and convert to subtitle format if needed
      // This will depend on the actual response format from the /recogn endpoint

    } catch (error) {
      console.error("Error extracting subtitle from audio:", error);
      toast.error(`Failed to extract subtitle from audio: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsExtracting(false);
    }
  };

  const handleBack = () => {
    stackRef.current?.pop();
  };

  // Memoize the video URL to prevent unnecessary re-renders
  const finalVideoUrl = useMemo(() => {
    const videoUrl = currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success'
      ? currentTask.DownloadTask.videoUrl
      : currentTask.videoUrl;

    if (!videoUrl) return null;

    // Use proxy for external URLs, direct URL for local/uploaded videos
    return videoUrl.startsWith('http') && !videoUrl.includes(window.location.hostname)
      ? `/api/302/vt/video/proxy?url=${encodeURIComponent(videoUrl)}`
      : videoUrl;
  }, [currentTask.DownloadTask?.videoUrl, currentTask.DownloadTask?.downloadStatus, currentTask.videoUrl]);

  // Memoize the poster URL to prevent unnecessary re-renders
  const posterUrl = useMemo(() => {
    if (!currentTask.thumbnail) return undefined;

    return currentTask.thumbnail.startsWith('data:')
      ? currentTask.thumbnail
      : `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail)}`;
  }, [currentTask.thumbnail]);

  // Stable getInstance callback to prevent ArtPlayer recreation
  const getInstance = useCallback((art: any) => {
    // Store reference for manual control
    artPlayerRef.current = art;
    console.log('[ARTPLAYER] Instance created:', art);
    console.log('[ARTPLAYER] Initial state - playing:', art.playing, 'currentTime:', art.currentTime);

    // Set up event listeners properly
    art.on('ready', () => {
      console.log('[ARTPLAYER] Video is ready');
      console.log('[ARTPLAYER] Video duration:', art.duration);
      console.log('[ARTPLAYER] Video URL:', art.url);
      setVideoReady(true);
    });

    // Only track time updates, don't interfere with play/pause
    art.on('video:timeupdate', () => {
      if (art.playing) {
        const currentVideoTime = art.currentTime;
        console.log('[ARTPLAYER] Time update:', currentVideoTime.toFixed(1), 'playing:', art.playing);
        setCurrentTime(currentVideoTime);
      }
    });

    // Track play/pause state from actual video events
    art.on('video:play', () => {
      console.log('[ARTPLAYER] Video play event fired');
      console.log('[ARTPLAYER] Video state - playing:', art.playing, 'currentTime:', art.currentTime);
      setIsVideoPlaying(true);
      setIsTimerRunning(false); // Stop manual timer when video plays
    });

    art.on('video:pause', () => {
      console.log('[ARTPLAYER] Video pause event fired');
      console.log('[ARTPLAYER] Video state - playing:', art.playing, 'currentTime:', art.currentTime);
      setIsVideoPlaying(false);
    });

    art.on('video:ended', () => {
      console.log('[ARTPLAYER] Video ended event');
      setIsVideoPlaying(false);
    });

    art.on('video:canplay', () => {
      console.log('[ARTPLAYER] Video can play');
    });

    art.on('video:waiting', () => {
      console.log('[ARTPLAYER] Video waiting/buffering');
    });

    // Handle video errors gracefully
    art.on('video:error', (error: any) => {
      console.error('[ARTPLAYER] Video error:', error);
      setVideoReady(false);
      setIsVideoPlaying(false);
    });
  }, []); // Empty dependency array - this callback should never change

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")} with Video Preview
          </h2>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex-1 flex">
        {/* Video Preview Panel */}
        <div className="w-1/2 border-r relative">
          {/* Video Player */}
          <div
            className={cn(
              "p-4 sticky top-5 z-20 transition-all duration-300 ease-in-out",
              isVideoFollowing
                ? "shadow-xl backdrop-blur-md border border-primary/30 bg-card/80 rounded-xl"
                : "bg-transparent rounded-lg"
            )}
            ref={videoContainerRef}
          >
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              {(() => {
                // Handle case where we have thumbnail but no video URL (thumbnail preview)
                if (!finalVideoUrl && currentTask.thumbnail) {
                  return (
                    <div className="relative w-full h-full">
                      <Image
                        src={posterUrl || currentTask.thumbnail}
                        alt={currentTask.name || "Video thumbnail"}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 100vw, 50vw"
                        priority
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <div className="text-center text-white">
                          <div className="text-sm font-medium">Video Preview</div>
                          <div className="text-xs opacity-75">Thumbnail only</div>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle video playback
                if (finalVideoUrl) {
                  const isYoutube = isYoutubeUrl(finalVideoUrl);
                  const youtubeVideoId = isYoutube ? getYoutubeVideoId(finalVideoUrl) : null;

                  if (isYoutube && youtubeVideoId) {
                    return (
                      <iframe
                        src={`https://www.youtube.com/embed/${youtubeVideoId}`}
                        className="absolute inset-0 size-full"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    );
                  } else {
                    return (
                      <div className="relative w-full h-full">
                        <ArtPlayer
                          key={finalVideoUrl} // Force recreation only when URL changes
                          url={finalVideoUrl}
                          poster={posterUrl}
                          className="absolute inset-0 size-full"
                          getInstance={getInstance}
                        />
                        {/* Add subtitle overlay back but in a non-interfering way */}
                        <div className="absolute inset-0 pointer-events-none flex flex-col justify-end p-4 z-10">
                          {currentSubtitle && (currentSubtitle.text || currentSubtitle.translatedText) && (
                            <>
                              {currentSubtitle.translatedText && (
                                <div className="text-center mb-2">
                                  <div className="inline-block bg-black/80 text-white px-3 py-1 rounded text-sm font-medium shadow-lg">
                                    {currentSubtitle.translatedText}
                                  </div>
                                </div>
                              )}
                              {currentSubtitle.text && (
                                <div className="text-center">
                                  <div className="inline-block bg-black/80 text-white px-3 py-1 rounded text-sm shadow-lg">
                                    {currentSubtitle.text}
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    );
                  }
                }

                // Default fallback - no video or thumbnail
                return (
                  <div className="w-full h-full flex items-center justify-center text-white">
                    <div className="text-center space-y-2">
                      <div className="text-sm font-medium">No video loaded</div>
                      <div className="text-xs text-muted-foreground">Please select a video first</div>
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* Manual Time Control */}
            <div className="mt-3 space-y-2">
              <div className="text-sm text-muted-foreground">
                <div className="flex items-center justify-between">
                  <span>Manual Time: {currentTime.toFixed(1)}s</span>
                  <div className="flex items-center gap-2">
                    {videoReady && (
                      <span className="text-green-600 text-xs">● Video Ready</span>
                    )}
                    {isVideoFollowing && (
                      <span className="text-blue-600 text-xs animate-pulse">📍 Following Scroll</span>
                    )}
                    {currentSubtitle && (
                      <span className="text-primary font-medium">
                        Active: {currentSubtitle.startTime} - {currentSubtitle.endTime}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced manual time input and controls with video integration */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    if (isVideoPlaying || isTimerRunning) {
                      handleVideoPause();
                    } else {
                      handleVideoPlay();
                    }
                  }}
                  className="h-8 w-8 p-0"
                  title="Play/Pause video and timer"
                >
                  {(isVideoPlaying || isTimerRunning) ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                </Button>
                <Input
                  type="number"
                  value={currentTime}
                  onChange={(e) => {
                    const newTime = parseFloat(e.target.value) || 0;
                    console.log('[MANUAL] Time input changed to:', newTime);
                    setCurrentTime(newTime);
                    // Also seek video when manually changing time
                    if (artPlayerRef.current && videoReady) {
                      try {
                        console.log('[MANUAL] Seeking video to manual time:', newTime);
                        artPlayerRef.current.seek = newTime;
                      } catch (error) {
                        console.warn('[MANUAL] Video seek failed during manual time change:', error);
                      }
                    } else {
                      console.log('[MANUAL] Video seek skipped - not ready');
                    }
                  }}
                  className="w-20 h-8 text-xs"
                  step="0.1"
                  min="0"
                  placeholder="0.0"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSyncWithVideo}
                  className="h-8 w-8 p-0"
                  title="Sync timer with current video time"
                  disabled={!videoReady}
                >
                  🔄
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    console.log('[RESET] Reset button clicked');
                    setCurrentTime(0);
                    if (artPlayerRef.current && videoReady) {
                      try {
                        console.log('[RESET] Seeking video to 0');
                        artPlayerRef.current.seek = 0;
                        console.log('[RESET] Video reset successful');
                      } catch (error) {
                        console.warn('[RESET] Video reset failed:', error);
                      }
                    } else {
                      console.log('[RESET] Video reset skipped - not ready');
                    }
                  }}
                  className="h-8 text-xs"
                  title="Reset video and timer to start"
                >
                  Reset
                </Button>
                <span className="text-xs text-muted-foreground">
                  Video + Timer Control
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Subtitle Editor Panel */}
        <div className="w-1/2 flex flex-col h-screen overflow-y-auto ml-auto" ref={subtitleListRef}>
          {/* Action Bar - Sticky */}
          <div className="sticky top-0 z-10 bg-background border-b shadow-sm">
            <div className="flex items-center gap-2 bg-muted/30 p-3">
              <Button
                size="sm"
                variant="default"
                onClick={() => setShowPromptInput(!showPromptInput)}
                disabled={isGenerating || isTranslating || isExtracting}
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Create with AI
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleExtractSubtitleFromAudio}
                disabled={isExtracting || isGenerating || isTranslating}
              >
                {isExtracting ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <AudioLines className="h-3 w-3 mr-1" />
                )}
                Extract from Audio
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={handleTranslateAllWithAI}
                disabled={isTranslating || isGenerating || isExtracting || subtitleData.originalSubtitles.length === 0}
              >
                {isTranslating ? (
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Languages className="h-3 w-3 mr-1" />
                )}
                Translate All
              </Button>
              <div className="ml-auto text-xs text-muted-foreground">
                {subtitleData.originalSubtitles.length} subtitles
              </div>
            </div>
          </div>

          {/* AI Prompt Input */}
          {showPromptInput && (
            <div className="border-b bg-muted/20 p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Wand2 className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Create Subtitles with AI</h3>
                </div>
                <Textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  placeholder="Describe the audio source (e.g., 'this lecture on quantum physics', 'the dialogue in this short film')"
                  className="min-h-[80px]"
                />
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={handleGenerateAISubtitles}
                    disabled={!promptText.trim() || isGenerating || isExtracting}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-3 w-3 mr-1" />
                        Generate
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowPromptInput(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          {subtitleData.originalSubtitles.length > 0 ? (
            <div className="flex-1 overflow-y-auto">
              {/* Header Row */}
              <div className="sticky top-0 z-10 bg-background border-b">
                <div className="grid grid-cols-6 gap-2 p-3 text-xs font-medium text-muted-foreground bg-muted/30">
                  <div>Start Time</div>
                  <div>End Time</div>
                  <div>Original Text</div>
                  <div>Translation</div>
                  <div>Duration</div>
                  <div>Actions</div>
                </div>
              </div>

              {/* Subtitle Rows */}
              <div className="divide-y">
                {subtitleData.originalSubtitles.map((subtitle) => (
                  <SubtitleRow
                    key={subtitle.id}
                    subtitle={subtitle}
                    editingField={editingField?.id === subtitle.id ? editingField.field : null}
                    onFieldEdit={(field) =>
                      setEditingField(field ? { id: subtitle.id, field } : null)
                    }
                    onSave={(updates) => {
                      updateBothSubtitles({ id: subtitle.id, updates });
                    }}
                    onDelete={() => deleteSubtitle(subtitle.id)}
                    isActive={currentSubtitle?.id === subtitle.id}
                    onSeekTo={handleSeekTo}
                  />
                ))}
              </div>

              {/* Add Subtitle Button */}
              <div className="p-4 border-t bg-muted/10">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddSubtitle}
                  className="w-full"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Subtitle at {currentTime.toFixed(1)}s
                </Button>
              </div>
            </div>
          ) : (
            <div className="min-h-[400px] flex items-center justify-center">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <Edit3 className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click "Add Subtitle" to create your first subtitle.
                  </p>
                </div>
                <Button
                  size="sm"
                  onClick={handleAddSubtitle}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add First Subtitle
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
